'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useAgentZero, Agent, ChatMessage } from '@/hooks/use-agent-zero';
import { 
  Bot, 
  MessageCircle, 
  Activity, 
  Zap, 
  Users, 
  TrendingUp,
  Send,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

export function AgentControlPanel() {
  const { 
    agents, 
    status, 
    loading, 
    chatMessages, 
    checkStatus, 
    fetchAgents, 
    sendMessage, 
    clearChat 
  } = useAgentZero();
  
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [sending, setSending] = useState(false);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sending) return;
    
    setSending(true);
    try {
      await sendMessage(messageInput, selectedAgent?.id);
      setMessageInput('');
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setSending(false);
    }
  };

  const getStatusIcon = (agentStatus: string) => {
    switch (agentStatus) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'busy':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (agentStatus: string) => {
    switch (agentStatus) {
      case 'active':
        return 'bg-green-500';
      case 'busy':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Agent Zero Status */}
      <Card className="lg:col-span-3">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <CardTitle className="text-lg">Agent Zero Control</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              status.status === 'connected' ? 'bg-green-500' : 
              status.status === 'error' ? 'bg-red-500' : 'bg-gray-500'
            }`} />
            <Badge variant={status.status === 'connected' ? 'default' : 'destructive'}>
              {status.status}
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => { checkStatus(); fetchAgents(); }}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <CardDescription>
            {status.status === 'connected' 
              ? `Connected to Agent Zero - ${agents.length} agents available`
              : status.message || 'Disconnected from Agent Zero'
            }
          </CardDescription>
        </CardContent>
      </Card>

      {/* Agents List */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Active Agents</span>
          </CardTitle>
          <CardDescription>
            Manage and monitor your AI agents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <div className="space-y-3">
              {/* Agent 1 - Active (Orange/Yellow) */}
              <div className="bg-gradient-to-r from-orange-900/30 to-yellow-900/30 border-2 border-orange-500/50 rounded-xl p-4 hover:border-orange-400 transition-all duration-300 cursor-pointer">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-white font-bold text-lg">Agent 1</h3>
                  <span className="bg-orange-500 text-black px-3 py-1 rounded-full text-xs font-bold">ACTIVE</span>
                </div>
                <div className="space-y-2">
                  <div>
                    <span className="text-yellow-400 font-medium">Current Task:</span>
                    <div className="text-white">Analyzing data</div>
                  </div>
                  <div>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-white text-sm">Performance</span>
                      <span className="text-white font-bold">75%</span>
                    </div>
                    <div className="w-full bg-slate-800 rounded-full h-2">
                      <div className="bg-gradient-to-r from-orange-500 to-yellow-400 h-2 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Agent 2 - Thinking (Blue) */}
              <div className="bg-gradient-to-r from-blue-900/30 to-cyan-900/30 border-2 border-blue-500/50 rounded-xl p-4 hover:border-blue-400 transition-all duration-300 cursor-pointer">
                <div className="flex items-center justify-between">
                  <h3 className="text-white font-bold text-lg">Agent 2</h3>
                  <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-bold">THINKING</span>
                </div>
              </div>

              {/* Agent 3 - Error (Red) */}
              <div className="bg-gradient-to-r from-red-900/30 to-red-800/30 border-2 border-red-500/50 rounded-xl p-4 hover:border-red-400 transition-all duration-300 cursor-pointer">
                <div className="flex items-center justify-between mb-3">
                  <h3 className="text-white font-bold text-lg">Agent 3</h3>
                  <span className="bg-red-500 text-white px-3 py-1 rounded-full text-xs font-bold">ERROR</span>
                </div>
                <div>
                  <div className="flex justify-between items-center mb-1">
                    <span className="text-white text-sm">Performance</span>
                    <span className="text-white font-bold">45%</span>
                  </div>
                  <div className="w-full bg-slate-800 rounded-full h-2">
                    <div className="bg-gradient-to-r from-red-500 to-red-400 h-2 rounded-full" style={{ width: '45%' }}></div>
                  </div>
                </div>
              </div>

              {/* Agent 4 - Inactive (Dark) */}
              <div className="bg-slate-800/50 border-2 border-slate-600/50 rounded-xl p-4 hover:border-slate-500 transition-all duration-300 cursor-pointer">
                <h3 className="text-slate-400 font-bold text-lg">Agent 4</h3>
              </div>

              {/* Show dynamic agents if any exist */}
              {agents.map((agent) => (
                <div
                  key={agent.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                    selectedAgent?.id === agent.id
                      ? 'border-primary bg-primary/5'
                      : 'border-border hover:bg-muted/50'
                  }`}
                  onClick={() => setSelectedAgent(agent)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(agent.status)}
                      <h4 className="font-medium">{agent.name}</h4>
                    </div>
                    <Badge variant="outline">{agent.type}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    {agent.description}
                  </p>
                  {agent.performance && (
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="text-center">
                        <div className="font-medium">{agent.performance.tasksCompleted}</div>
                        <div className="text-muted-foreground">Tasks</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{agent.performance.successRate}%</div>
                        <div className="text-muted-foreground">Success</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{agent.performance.avgResponseTime}</div>
                        <div className="text-muted-foreground">Avg Time</div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>

          {/* Command Input */}
          <div className="p-4 border-t border-slate-700">
            <div className="flex space-x-2">
              <input
                type="text"
                placeholder="Enter agent command..."
                className="flex-1 bg-slate-800 border border-slate-600 rounded-lg px-3 py-2 text-white placeholder-slate-400 focus:outline-none focus:border-orange-500"
              />
              <button className="bg-red-600 hover:bg-red-700 text-white p-2 rounded-lg transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M7 4a3 3 0 016 0v4a3 3 0 11-6 0V4zm4 10.93A7.001 7.001 0 0017 8a1 1 0 10-2 0A5 5 0 015 8a1 1 0 00-2 0 7.001 7.001 0 006 6.93V17H6a1 1 0 100 2h8a1 1 0 100-2h-3v-2.07z" clipRule="evenodd" />
                </svg>
              </button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Chat Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5" />
            <span>Agent Chat</span>
          </CardTitle>
          <CardDescription>
            {selectedAgent 
              ? `Chatting with ${selectedAgent.name}`
              : 'Select an agent to start chatting'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <ScrollArea className="h-[250px] border rounded-md p-3">
            <div className="space-y-3">
              {chatMessages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] p-2 rounded-lg text-sm ${
                      msg.sender === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    {msg.message}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
          
          <div className="flex space-x-2">
            <Input
              placeholder={selectedAgent ? "Type a message..." : "Select an agent first"}
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              disabled={!selectedAgent || sending}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={!selectedAgent || !messageInput.trim() || sending}
              size="sm"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          {chatMessages.length > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearChat}
              className="w-full"
            >
              Clear Chat
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
