import { NextRequest, NextResponse } from 'next/server';

// WebSocket connection info endpoint
export async function GET() {
  const AGENT_ZERO_WS_URL = process.env.AGENT_ZERO_WS_URL || 'ws://localhost:50001/ws';
  
  return NextResponse.json({
    message: 'WebSocket connection information',
    websocket: {
      agentZeroUrl: AGENT_ZERO_WS_URL,
      protocols: ['agent-zero-protocol'],
      reconnectInterval: 5000,
      maxReconnectAttempts: 10
    },
    usage: {
      connect: `Connect to ${AGENT_ZERO_WS_URL} for real-time agent updates`,
      messageFormat: {
        type: 'string (required)',
        payload: 'object (optional)',
        sessionId: 'string (optional)'
      },
      messageTypes: [
        'agent-status',
        'chat-message',
        'task-update',
        'system-status'
      ]
    },
    example: {
      send: {
        type: 'chat-message',
        payload: {
          message: 'Hello Agent Zero',
          agentId: 'lead-processor'
        },
        sessionId: 'crm-session-123'
      },
      receive: {
        type: 'agent-response',
        payload: {
          message: 'Hello! How can I help you with lead processing?',
          agentId: 'lead-processor',
          timestamp: '2025-06-29T16:20:00Z'
        },
        sessionId: 'crm-session-123'
      }
    }
  });
}

// WebSocket proxy configuration
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { action, sessionId, agentId } = body;

    if (action === 'create-session') {
      // Create a new WebSocket session
      const sessionData = {
        sessionId: sessionId || `crm-ws-${Date.now()}`,
        agentId: agentId || 'default',
        created: new Date().toISOString(),
        status: 'active'
      };

      return NextResponse.json({
        success: true,
        session: sessionData,
        websocketUrl: process.env.AGENT_ZERO_WS_URL || 'ws://localhost:50001/ws',
      });
    }

    if (action === 'close-session') {
      return NextResponse.json({
        success: true,
        message: 'Session closed',
        sessionId
      });
    }

    return NextResponse.json(
      { error: 'Invalid action. Supported actions: create-session, close-session' },
      { status: 400 }
    );
  } catch (error) {
    console.error('Error in WebSocket API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
