"""
CRM Sales Assistant Agent
Specialized agent for sales process automation and customer interaction support
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent import Agent, AgentContext
from python.helpers.print_style import PrintStyle
from python.helpers import files
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

class CRMSalesAssistant(Agent):
    """
    Specialized agent for CRM sales assistance including:
    - Opportunity management and tracking
    - Sales process automation
    - Proposal and quote generation
    - Follow-up scheduling and reminders
    - Sales performance analysis
    """
    
    def __init__(self, number: int, config: Dict[str, Any]):
        super().__init__(number, config)
        self.agent_name = "CRM Sales Assistant"
        self.agent_description = "Assists with sales processes and customer interactions"
        
        # Sales stages
        self.sales_stages = [
            "prospecting", "qualification", "needs_analysis", 
            "proposal", "negotiation", "closing", "won", "lost"
        ]
        
        # Deal sizes and probabilities
        self.deal_probabilities = {
            "prospecting": 10,
            "qualification": 20,
            "needs_analysis": 40,
            "proposal": 60,
            "negotiation": 80,
            "closing": 90,
            "won": 100,
            "lost": 0
        }
        
        # Follow-up templates
        self.follow_up_templates = {
            "initial_contact": {
                "subject": "Thank you for your interest in {product}",
                "timing": "immediate",
                "priority": "high"
            },
            "demo_follow_up": {
                "subject": "Following up on our demo - Next steps",
                "timing": "1 day",
                "priority": "high"
            },
            "proposal_follow_up": {
                "subject": "Proposal review and questions",
                "timing": "3 days",
                "priority": "medium"
            },
            "negotiation_follow_up": {
                "subject": "Contract terms discussion",
                "timing": "2 days",
                "priority": "high"
            }
        }
    
    def get_system_prompt(self) -> str:
        return f"""You are {self.agent_name}, a specialized AI agent for CRM sales assistance.

Your primary responsibilities:
1. **Opportunity Management**: Track and manage sales opportunities through the pipeline
2. **Sales Process Automation**: Automate routine sales tasks and workflows
3. **Proposal Generation**: Create and customize sales proposals and quotes
4. **Follow-up Management**: Schedule and track follow-up activities
5. **Performance Analysis**: Analyze sales metrics and provide insights

**Sales Pipeline Stages:**
{', '.join(self.sales_stages)}

**Stage Probabilities:**
{json.dumps(self.deal_probabilities, indent=2)}

**Key Guidelines:**
- Always maintain accurate opportunity records
- Follow up promptly based on stage and priority
- Personalize communications based on customer data
- Track all interactions and outcomes
- Provide data-driven insights and recommendations
- Escalate high-value or complex deals appropriately

**Response Format:**
When managing opportunities, provide:
1. Current stage assessment
2. Recommended next actions
3. Follow-up schedule
4. Risk assessment
5. Revenue projections

Be proactive, customer-focused, and always aim to advance opportunities through the pipeline while maintaining relationship quality."""

    async def manage_opportunity(self, opportunity_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Manage a sales opportunity through the pipeline
        """
        try:
            PrintStyle(self.agent_name).print(f"Managing opportunity: {opportunity_data.get('name', 'Unknown')}")
            
            # Analyze current stage
            stage_analysis = self._analyze_current_stage(opportunity_data)
            
            # Generate next actions
            next_actions = self._generate_next_actions(opportunity_data, stage_analysis)
            
            # Calculate revenue projections
            revenue_projection = self._calculate_revenue_projection(opportunity_data)
            
            # Assess risks and opportunities
            risk_assessment = self._assess_risks(opportunity_data)
            
            # Generate follow-up plan
            follow_up_plan = self._create_follow_up_plan(opportunity_data, stage_analysis)
            
            result = {
                "opportunity_id": opportunity_data.get("id", f"opp_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                "current_data": opportunity_data,
                "stage_analysis": stage_analysis,
                "next_actions": next_actions,
                "revenue_projection": revenue_projection,
                "risk_assessment": risk_assessment,
                "follow_up_plan": follow_up_plan,
                "updated_at": datetime.now().isoformat(),
                "managed_by": self.agent_name
            }
            
            PrintStyle(self.agent_name).print(f"Opportunity managed. Stage: {stage_analysis['current_stage']}, Value: ${revenue_projection['weighted_value']}")
            return result
            
        except Exception as e:
            PrintStyle(self.agent_name).print(f"Error managing opportunity: {str(e)}")
            return {
                "error": str(e),
                "opportunity_data": opportunity_data,
                "updated_at": datetime.now().isoformat()
            }
    
    def _analyze_current_stage(self, opportunity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze the current stage of the opportunity"""
        current_stage = opportunity_data.get("stage", "prospecting").lower()
        
        # Validate stage
        if current_stage not in self.sales_stages:
            current_stage = "prospecting"
        
        # Calculate stage progression
        stage_index = self.sales_stages.index(current_stage)
        progression_percentage = (stage_index / (len(self.sales_stages) - 1)) * 100
        
        # Determine stage health
        days_in_stage = opportunity_data.get("days_in_current_stage", 0)
        stage_health = self._assess_stage_health(current_stage, days_in_stage)
        
        return {
            "current_stage": current_stage,
            "stage_index": stage_index,
            "progression_percentage": progression_percentage,
            "probability": self.deal_probabilities[current_stage],
            "days_in_stage": days_in_stage,
            "stage_health": stage_health
        }
    
    def _assess_stage_health(self, stage: str, days_in_stage: int) -> Dict[str, Any]:
        """Assess the health of the current stage based on time spent"""
        # Define healthy time ranges for each stage (in days)
        healthy_ranges = {
            "prospecting": 7,
            "qualification": 14,
            "needs_analysis": 21,
            "proposal": 14,
            "negotiation": 21,
            "closing": 7
        }
        
        healthy_max = healthy_ranges.get(stage, 14)
        
        if days_in_stage <= healthy_max:
            health = "healthy"
            concern_level = "none"
        elif days_in_stage <= healthy_max * 1.5:
            health = "attention_needed"
            concern_level = "low"
        elif days_in_stage <= healthy_max * 2:
            health = "at_risk"
            concern_level = "medium"
        else:
            health = "stalled"
            concern_level = "high"
        
        return {
            "health": health,
            "concern_level": concern_level,
            "days_in_stage": days_in_stage,
            "healthy_max": healthy_max,
            "recommendation": self._get_health_recommendation(health, stage)
        }
    
    def _get_health_recommendation(self, health: str, stage: str) -> str:
        """Get recommendation based on stage health"""
        if health == "healthy":
            return f"Opportunity is progressing well in {stage} stage"
        elif health == "attention_needed":
            return f"Consider accelerating activities in {stage} stage"
        elif health == "at_risk":
            return f"Urgent action needed to advance from {stage} stage"
        else:
            return f"Opportunity may be stalled in {stage} - consider re-qualification"
    
    def _generate_next_actions(self, opportunity_data: Dict[str, Any], stage_analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Generate recommended next actions based on current stage"""
        current_stage = stage_analysis["current_stage"]
        actions = []
        
        stage_actions = {
            "prospecting": [
                {"action": "research_prospect", "priority": "high", "timeline": "immediate"},
                {"action": "initial_outreach", "priority": "high", "timeline": "within 1 day"},
                {"action": "connect_on_linkedin", "priority": "medium", "timeline": "within 2 days"}
            ],
            "qualification": [
                {"action": "discovery_call", "priority": "high", "timeline": "within 3 days"},
                {"action": "send_qualification_questions", "priority": "medium", "timeline": "within 1 day"},
                {"action": "research_company_needs", "priority": "medium", "timeline": "within 2 days"}
            ],
            "needs_analysis": [
                {"action": "conduct_needs_assessment", "priority": "high", "timeline": "within 1 week"},
                {"action": "schedule_demo", "priority": "high", "timeline": "within 5 days"},
                {"action": "identify_decision_makers", "priority": "medium", "timeline": "within 3 days"}
            ],
            "proposal": [
                {"action": "prepare_custom_proposal", "priority": "high", "timeline": "within 3 days"},
                {"action": "schedule_proposal_presentation", "priority": "high", "timeline": "within 1 week"},
                {"action": "gather_technical_requirements", "priority": "medium", "timeline": "within 2 days"}
            ],
            "negotiation": [
                {"action": "review_contract_terms", "priority": "high", "timeline": "within 2 days"},
                {"action": "address_objections", "priority": "high", "timeline": "immediate"},
                {"action": "prepare_final_offer", "priority": "medium", "timeline": "within 3 days"}
            ],
            "closing": [
                {"action": "send_final_contract", "priority": "high", "timeline": "immediate"},
                {"action": "schedule_closing_call", "priority": "high", "timeline": "within 1 day"},
                {"action": "prepare_onboarding_materials", "priority": "low", "timeline": "within 1 week"}
            ]
        }
        
        return stage_actions.get(current_stage, [])
    
    def _calculate_revenue_projection(self, opportunity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate revenue projections for the opportunity"""
        deal_value = opportunity_data.get("value", 0)
        current_stage = opportunity_data.get("stage", "prospecting").lower()
        probability = self.deal_probabilities.get(current_stage, 10)
        
        weighted_value = deal_value * (probability / 100)
        
        # Calculate potential close date based on stage
        stage_index = self.sales_stages.index(current_stage) if current_stage in self.sales_stages else 0
        remaining_stages = len(self.sales_stages) - stage_index - 1
        estimated_days_to_close = remaining_stages * 14  # Assume 2 weeks per stage
        
        estimated_close_date = datetime.now() + timedelta(days=estimated_days_to_close)
        
        return {
            "deal_value": deal_value,
            "probability": probability,
            "weighted_value": round(weighted_value, 2),
            "estimated_close_date": estimated_close_date.isoformat(),
            "days_to_close": estimated_days_to_close
        }
    
    def _assess_risks(self, opportunity_data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess risks associated with the opportunity"""
        risks = []
        risk_score = 0
        
        # Budget risk
        if not opportunity_data.get("budget_confirmed"):
            risks.append("Budget not confirmed")
            risk_score += 2
        
        # Decision maker risk
        if not opportunity_data.get("decision_maker_identified"):
            risks.append("Decision maker not identified")
            risk_score += 3
        
        # Timeline risk
        if not opportunity_data.get("timeline_defined"):
            risks.append("Timeline not defined")
            risk_score += 2
        
        # Competition risk
        if opportunity_data.get("competitors_involved"):
            risks.append("Competitors involved")
            risk_score += 2
        
        # Determine overall risk level
        if risk_score <= 2:
            risk_level = "low"
        elif risk_score <= 5:
            risk_level = "medium"
        else:
            risk_level = "high"
        
        return {
            "risks": risks,
            "risk_score": risk_score,
            "risk_level": risk_level,
            "mitigation_actions": self._get_risk_mitigation_actions(risks)
        }
    
    def _get_risk_mitigation_actions(self, risks: List[str]) -> List[str]:
        """Get actions to mitigate identified risks"""
        mitigation_map = {
            "Budget not confirmed": "Schedule budget confirmation call",
            "Decision maker not identified": "Request introduction to decision maker",
            "Timeline not defined": "Discuss project timeline and urgency",
            "Competitors involved": "Conduct competitive analysis and differentiation"
        }
        
        return [mitigation_map.get(risk, f"Address: {risk}") for risk in risks]
    
    def _create_follow_up_plan(self, opportunity_data: Dict[str, Any], stage_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """Create a follow-up plan based on current stage and health"""
        current_stage = stage_analysis["current_stage"]
        stage_health = stage_analysis["stage_health"]["health"]
        
        # Determine follow-up urgency based on stage health
        if stage_health == "stalled":
            urgency = "immediate"
            frequency = "daily"
        elif stage_health == "at_risk":
            urgency = "within 1 day"
            frequency = "every 2 days"
        elif stage_health == "attention_needed":
            urgency = "within 2 days"
            frequency = "weekly"
        else:
            urgency = "within 1 week"
            frequency = "bi-weekly"
        
        # Get appropriate template
        template_key = f"{current_stage}_follow_up" if f"{current_stage}_follow_up" in self.follow_up_templates else "initial_contact"
        template = self.follow_up_templates.get(template_key, self.follow_up_templates["initial_contact"])
        
        return {
            "urgency": urgency,
            "frequency": frequency,
            "template": template,
            "next_follow_up": (datetime.now() + timedelta(days=1)).isoformat(),
            "method": "email_and_call" if stage_health in ["at_risk", "stalled"] else "email"
        }
