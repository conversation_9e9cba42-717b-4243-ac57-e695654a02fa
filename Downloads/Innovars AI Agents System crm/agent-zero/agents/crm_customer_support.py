"""
CRM Customer Support Agent
Specialized agent for customer support and service management
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent import Agent, AgentContext
from python.helpers.print_style import PrintStyle
from python.helpers import files
import json
import re
from datetime import datetime, timedelta
from typing import Dict, List, Any

class CRMCustomerSupport(Agent):
    """
    Specialized agent for CRM customer support including:
    - Ticket routing and prioritization
    - Knowledge base integration
    - Escalation management
    - Customer satisfaction tracking
    - Support analytics and reporting
    """
    
    def __init__(self, number: int, config: Dict[str, Any]):
        super().__init__(number, config)
        self.agent_name = "CRM Customer Support"
        self.agent_description = "Handles customer inquiries and support tickets"
        
        # Ticket priorities
        self.priority_levels = {
            "critical": {"sla_hours": 1, "escalation_hours": 2, "score": 5},
            "high": {"sla_hours": 4, "escalation_hours": 8, "score": 4},
            "medium": {"sla_hours": 24, "escalation_hours": 48, "score": 3},
            "low": {"sla_hours": 72, "escalation_hours": 120, "score": 2},
            "informational": {"sla_hours": 168, "escalation_hours": 240, "score": 1}
        }
        
        # Ticket categories
        self.ticket_categories = {
            "technical_issue": {"priority_boost": 1, "requires_specialist": True},
            "billing_inquiry": {"priority_boost": 0, "requires_specialist": False},
            "feature_request": {"priority_boost": -1, "requires_specialist": False},
            "bug_report": {"priority_boost": 1, "requires_specialist": True},
            "account_access": {"priority_boost": 2, "requires_specialist": False},
            "integration_support": {"priority_boost": 1, "requires_specialist": True},
            "general_inquiry": {"priority_boost": 0, "requires_specialist": False}
        }
        
        # Customer tiers
        self.customer_tiers = {
            "enterprise": {"priority_boost": 2, "sla_multiplier": 0.5},
            "premium": {"priority_boost": 1, "sla_multiplier": 0.75},
            "standard": {"priority_boost": 0, "sla_multiplier": 1.0},
            "basic": {"priority_boost": -1, "sla_multiplier": 1.5}
        }
        
        # Response templates
        self.response_templates = {
            "acknowledgment": "Thank you for contacting support. We have received your request and will respond within {sla_time}.",
            "escalation": "Your ticket has been escalated to our specialist team for priority handling.",
            "resolution": "Your issue has been resolved. Please let us know if you need any further assistance.",
            "follow_up": "We wanted to follow up on your recent support request to ensure everything is working properly."
        }
    
    def get_system_prompt(self) -> str:
        return f"""You are {self.agent_name}, a specialized AI agent for CRM customer support management.

Your primary responsibilities:
1. **Ticket Routing**: Analyze and route support tickets to appropriate teams
2. **Priority Assessment**: Determine ticket priority based on impact and urgency
3. **Knowledge Base**: Provide solutions from knowledge base when possible
4. **Escalation Management**: Escalate complex issues to appropriate specialists
5. **Customer Communication**: Maintain professional and helpful communication
6. **SLA Monitoring**: Track and ensure SLA compliance

**Priority Levels:**
{json.dumps(self.priority_levels, indent=2)}

**Ticket Categories:**
{json.dumps(self.ticket_categories, indent=2)}

**Customer Tiers:**
{json.dumps(self.customer_tiers, indent=2)}

**Key Guidelines:**
- Always acknowledge tickets promptly
- Assess priority accurately based on impact and customer tier
- Provide clear, helpful responses
- Escalate when necessary
- Track resolution times and customer satisfaction
- Maintain detailed ticket history

**Response Format:**
When processing tickets, provide:
1. Ticket classification and priority
2. Recommended routing/assignment
3. Initial response or solution
4. SLA timeline
5. Escalation criteria

Be empathetic, professional, and solution-focused in all customer interactions."""

    async def process_ticket(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a customer support ticket
        """
        try:
            PrintStyle(self.agent_name).print(f"Processing ticket: {ticket_data.get('subject', 'No subject')}")
            
            # Classify the ticket
            classification = self._classify_ticket(ticket_data)
            
            # Determine priority
            priority_assessment = self._assess_priority(ticket_data, classification)
            
            # Calculate SLA
            sla_info = self._calculate_sla(priority_assessment, ticket_data)
            
            # Generate initial response
            initial_response = self._generate_initial_response(ticket_data, classification, sla_info)
            
            # Determine routing
            routing = self._determine_routing(classification, priority_assessment)
            
            # Check for escalation needs
            escalation_check = self._check_escalation_criteria(ticket_data, priority_assessment)
            
            result = {
                "ticket_id": ticket_data.get("id", f"ticket_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                "original_data": ticket_data,
                "classification": classification,
                "priority_assessment": priority_assessment,
                "sla_info": sla_info,
                "initial_response": initial_response,
                "routing": routing,
                "escalation_check": escalation_check,
                "processed_at": datetime.now().isoformat(),
                "processor": self.agent_name
            }
            
            PrintStyle(self.agent_name).print(f"Ticket processed. Priority: {priority_assessment['final_priority']}, Category: {classification['category']}")
            return result
            
        except Exception as e:
            PrintStyle(self.agent_name).print(f"Error processing ticket: {str(e)}")
            return {
                "error": str(e),
                "ticket_data": ticket_data,
                "processed_at": datetime.now().isoformat()
            }
    
    def _classify_ticket(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Classify the ticket based on content and metadata"""
        subject = ticket_data.get("subject", "").lower()
        description = ticket_data.get("description", "").lower()
        content = f"{subject} {description}"
        
        # Keyword-based classification
        classification_keywords = {
            "technical_issue": ["error", "bug", "crash", "not working", "broken", "issue", "problem"],
            "billing_inquiry": ["billing", "invoice", "payment", "charge", "refund", "subscription"],
            "feature_request": ["feature", "enhancement", "suggestion", "improvement", "add"],
            "bug_report": ["bug", "defect", "error", "malfunction", "incorrect"],
            "account_access": ["login", "password", "access", "account", "locked", "reset"],
            "integration_support": ["integration", "api", "webhook", "sync", "connect"],
            "general_inquiry": ["question", "how to", "help", "information", "inquiry"]
        }
        
        # Score each category
        category_scores = {}
        for category, keywords in classification_keywords.items():
            score = sum(1 for keyword in keywords if keyword in content)
            category_scores[category] = score
        
        # Determine best match
        best_category = max(category_scores, key=category_scores.get) if any(category_scores.values()) else "general_inquiry"
        confidence = category_scores[best_category] / max(1, len(classification_keywords[best_category]))
        
        return {
            "category": best_category,
            "confidence": min(confidence, 1.0),
            "category_scores": category_scores,
            "requires_specialist": self.ticket_categories[best_category]["requires_specialist"]
        }
    
    def _assess_priority(self, ticket_data: Dict[str, Any], classification: Dict[str, Any]) -> Dict[str, Any]:
        """Assess ticket priority based on multiple factors"""
        # Base priority from user input or default
        base_priority = ticket_data.get("priority", "medium").lower()
        if base_priority not in self.priority_levels:
            base_priority = "medium"
        
        base_score = self.priority_levels[base_priority]["score"]
        
        # Apply category boost
        category = classification["category"]
        category_boost = self.ticket_categories[category]["priority_boost"]
        
        # Apply customer tier boost
        customer_tier = ticket_data.get("customer_tier", "standard").lower()
        tier_boost = self.customer_tiers.get(customer_tier, {"priority_boost": 0})["priority_boost"]
        
        # Calculate final score
        final_score = base_score + category_boost + tier_boost
        final_score = max(1, min(5, final_score))  # Clamp between 1-5
        
        # Map score back to priority level
        score_to_priority = {5: "critical", 4: "high", 3: "medium", 2: "low", 1: "informational"}
        final_priority = score_to_priority[final_score]
        
        return {
            "base_priority": base_priority,
            "base_score": base_score,
            "category_boost": category_boost,
            "tier_boost": tier_boost,
            "final_score": final_score,
            "final_priority": final_priority,
            "customer_tier": customer_tier
        }
    
    def _calculate_sla(self, priority_assessment: Dict[str, Any], ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate SLA timelines based on priority and customer tier"""
        priority = priority_assessment["final_priority"]
        customer_tier = priority_assessment["customer_tier"]
        
        # Get base SLA hours
        base_sla_hours = self.priority_levels[priority]["sla_hours"]
        escalation_hours = self.priority_levels[priority]["escalation_hours"]
        
        # Apply customer tier multiplier
        tier_multiplier = self.customer_tiers.get(customer_tier, {"sla_multiplier": 1.0})["sla_multiplier"]
        
        # Calculate final SLA
        final_sla_hours = base_sla_hours * tier_multiplier
        final_escalation_hours = escalation_hours * tier_multiplier
        
        # Calculate deadlines
        created_at = datetime.fromisoformat(ticket_data.get("created_at", datetime.now().isoformat()))
        sla_deadline = created_at + timedelta(hours=final_sla_hours)
        escalation_deadline = created_at + timedelta(hours=final_escalation_hours)
        
        return {
            "sla_hours": final_sla_hours,
            "escalation_hours": final_escalation_hours,
            "sla_deadline": sla_deadline.isoformat(),
            "escalation_deadline": escalation_deadline.isoformat(),
            "time_remaining": (sla_deadline - datetime.now()).total_seconds() / 3600,  # hours
            "is_overdue": datetime.now() > sla_deadline
        }
    
    def _generate_initial_response(self, ticket_data: Dict[str, Any], classification: Dict[str, Any], sla_info: Dict[str, Any]) -> Dict[str, Any]:
        """Generate initial response to the customer"""
        customer_name = ticket_data.get("customer_name", "Valued Customer")
        sla_hours = sla_info["sla_hours"]
        
        # Format SLA time
        if sla_hours < 1:
            sla_time = f"{int(sla_hours * 60)} minutes"
        elif sla_hours < 24:
            sla_time = f"{int(sla_hours)} hours"
        else:
            sla_time = f"{int(sla_hours / 24)} business days"
        
        # Generate response based on category
        category = classification["category"]
        
        if category == "technical_issue":
            response = f"Hello {customer_name}, thank you for reporting this technical issue. Our technical team will investigate and respond within {sla_time}."
        elif category == "billing_inquiry":
            response = f"Hello {customer_name}, thank you for your billing inquiry. Our billing team will review your account and respond within {sla_time}."
        elif category == "account_access":
            response = f"Hello {customer_name}, we understand the urgency of account access issues. Our team will assist you within {sla_time}."
        else:
            response = self.response_templates["acknowledgment"].format(sla_time=sla_time)
            response = f"Hello {customer_name}, {response}"
        
        return {
            "message": response,
            "template_used": "acknowledgment",
            "personalized": True,
            "sla_mentioned": True
        }
    
    def _determine_routing(self, classification: Dict[str, Any], priority_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Determine where to route the ticket"""
        category = classification["category"]
        priority = priority_assessment["final_priority"]
        requires_specialist = classification["requires_specialist"]
        
        # Routing logic
        routing_map = {
            "technical_issue": "technical_support",
            "billing_inquiry": "billing_team",
            "feature_request": "product_team",
            "bug_report": "development_team",
            "account_access": "account_management",
            "integration_support": "technical_support",
            "general_inquiry": "general_support"
        }
        
        primary_team = routing_map.get(category, "general_support")
        
        # Escalate high priority items
        if priority in ["critical", "high"]:
            escalated_team = f"{primary_team}_senior"
        else:
            escalated_team = primary_team
        
        return {
            "primary_team": primary_team,
            "assigned_team": escalated_team,
            "requires_specialist": requires_specialist,
            "routing_reason": f"Category: {category}, Priority: {priority}",
            "auto_assigned": True
        }
    
    def _check_escalation_criteria(self, ticket_data: Dict[str, Any], priority_assessment: Dict[str, Any]) -> Dict[str, Any]:
        """Check if ticket meets escalation criteria"""
        escalation_needed = False
        escalation_reasons = []
        
        # Priority-based escalation
        if priority_assessment["final_priority"] == "critical":
            escalation_needed = True
            escalation_reasons.append("Critical priority ticket")
        
        # Customer tier escalation
        if priority_assessment["customer_tier"] == "enterprise":
            escalation_needed = True
            escalation_reasons.append("Enterprise customer")
        
        # Keyword-based escalation
        content = f"{ticket_data.get('subject', '')} {ticket_data.get('description', '')}".lower()
        escalation_keywords = ["urgent", "emergency", "down", "outage", "critical", "escalate"]
        
        for keyword in escalation_keywords:
            if keyword in content:
                escalation_needed = True
                escalation_reasons.append(f"Contains escalation keyword: {keyword}")
                break
        
        return {
            "escalation_needed": escalation_needed,
            "escalation_reasons": escalation_reasons,
            "escalation_level": "immediate" if priority_assessment["final_priority"] == "critical" else "standard",
            "escalation_target": "senior_support" if escalation_needed else None
        }
