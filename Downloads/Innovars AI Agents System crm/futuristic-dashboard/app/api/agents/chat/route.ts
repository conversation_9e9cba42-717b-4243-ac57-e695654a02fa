import { NextRequest, NextResponse } from 'next/server';

const AGENT_ZERO_BASE_URL = process.env.AGENT_ZERO_BASE_URL || 'http://localhost:50001';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { message, agentId, sessionId } = body;

    if (!message) {
      return NextResponse.json(
        { error: 'Message is required' },
        { status: 400 }
      );
    }

    // Get CSRF token from Agent Zero
    const csrfResponse = await fetch(`${AGENT_ZERO_BASE_URL}/csrf_token`);
    if (!csrfResponse.ok) {
      return NextResponse.json(
        { error: 'Failed to get CSRF token' },
        { status: 500 }
      );
    }
    const csrfData = await csrfResponse.json();

    // Forward the chat request to Agent Zero message endpoint
    const response = await fetch(`${AGENT_ZERO_BASE_URL}/message`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-CSRF-Token': csrfData.token,
      },
      body: JSON.stringify({
        text: message,
        context: sessionId || `crm-${Date.now()}`,
        message_id: `crm-msg-${Date.now()}`,
      }),
    });

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        { 
          error: 'Agent Zero request failed',
          details: errorText,
          status: response.status 
        },
        { status: response.status }
      );
    }

    const data = await response.json();
    
    return NextResponse.json({
      success: true,
      response: data,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error in chat API:', error);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json({
    message: 'Chat API endpoint - use POST to send messages',
    endpoints: {
      POST: '/api/agents/chat - Send a message to Agent Zero',
      body: {
        message: 'string (required)',
        agentId: 'string (optional)',
        sessionId: 'string (optional)'
      }
    }
  });
}
