'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { useAgentZero, Agent, ChatMessage } from '@/hooks/use-agent-zero';
import { 
  Bot, 
  MessageCircle, 
  Activity, 
  Zap, 
  Users, 
  TrendingUp,
  Send,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

export function AgentControlPanel() {
  const { 
    agents, 
    status, 
    loading, 
    chatMessages, 
    checkStatus, 
    fetchAgents, 
    sendMessage, 
    clearChat 
  } = useAgentZero();
  
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [messageInput, setMessageInput] = useState('');
  const [sending, setSending] = useState(false);

  const handleSendMessage = async () => {
    if (!messageInput.trim() || sending) return;
    
    setSending(true);
    try {
      await sendMessage(messageInput, selectedAgent?.id);
      setMessageInput('');
    } catch (error) {
      console.error('Failed to send message:', error);
    } finally {
      setSending(false);
    }
  };

  const getStatusIcon = (agentStatus: string) => {
    switch (agentStatus) {
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'busy':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Activity className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (agentStatus: string) => {
    switch (agentStatus) {
      case 'active':
        return 'bg-green-500';
      case 'busy':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Agent Zero Status */}
      <Card className="lg:col-span-3">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="flex items-center space-x-2">
            <Bot className="h-5 w-5" />
            <CardTitle className="text-lg">Agent Zero Control</CardTitle>
          </div>
          <div className="flex items-center space-x-2">
            <div className={`w-2 h-2 rounded-full ${
              status.status === 'connected' ? 'bg-green-500' : 
              status.status === 'error' ? 'bg-red-500' : 'bg-gray-500'
            }`} />
            <Badge variant={status.status === 'connected' ? 'default' : 'destructive'}>
              {status.status}
            </Badge>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => { checkStatus(); fetchAgents(); }}
              disabled={loading}
            >
              <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <CardDescription>
            {status.status === 'connected' 
              ? `Connected to Agent Zero - ${agents.length} agents available`
              : status.message || 'Disconnected from Agent Zero'
            }
          </CardDescription>
        </CardContent>
      </Card>

      {/* Agents List */}
      <Card className="lg:col-span-2">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Users className="h-5 w-5" />
            <span>Active Agents</span>
          </CardTitle>
          <CardDescription>
            Manage and monitor your AI agents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[400px]">
            <div className="space-y-4">
              {agents.map((agent) => (
                <div
                  key={agent.id}
                  className={`p-4 rounded-lg border cursor-pointer transition-colors ${
                    selectedAgent?.id === agent.id 
                      ? 'border-primary bg-primary/5' 
                      : 'border-border hover:bg-muted/50'
                  }`}
                  onClick={() => setSelectedAgent(agent)}
                >
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center space-x-2">
                      {getStatusIcon(agent.status)}
                      <h4 className="font-medium">{agent.name}</h4>
                    </div>
                    <Badge variant="outline">{agent.type}</Badge>
                  </div>
                  <p className="text-sm text-muted-foreground mb-3">
                    {agent.description}
                  </p>
                  {agent.performance && (
                    <div className="grid grid-cols-3 gap-2 text-xs">
                      <div className="text-center">
                        <div className="font-medium">{agent.performance.tasksCompleted}</div>
                        <div className="text-muted-foreground">Tasks</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{agent.performance.successRate}%</div>
                        <div className="text-muted-foreground">Success</div>
                      </div>
                      <div className="text-center">
                        <div className="font-medium">{agent.performance.avgResponseTime}</div>
                        <div className="text-muted-foreground">Avg Time</div>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Chat Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageCircle className="h-5 w-5" />
            <span>Agent Chat</span>
          </CardTitle>
          <CardDescription>
            {selectedAgent 
              ? `Chatting with ${selectedAgent.name}`
              : 'Select an agent to start chatting'
            }
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <ScrollArea className="h-[250px] border rounded-md p-3">
            <div className="space-y-3">
              {chatMessages.map((msg) => (
                <div
                  key={msg.id}
                  className={`flex ${msg.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-[80%] p-2 rounded-lg text-sm ${
                      msg.sender === 'user'
                        ? 'bg-primary text-primary-foreground'
                        : 'bg-muted'
                    }`}
                  >
                    {msg.message}
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
          
          <div className="flex space-x-2">
            <Input
              placeholder={selectedAgent ? "Type a message..." : "Select an agent first"}
              value={messageInput}
              onChange={(e) => setMessageInput(e.target.value)}
              onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
              disabled={!selectedAgent || sending}
            />
            <Button 
              onClick={handleSendMessage}
              disabled={!selectedAgent || !messageInput.trim() || sending}
              size="sm"
            >
              <Send className="h-4 w-4" />
            </Button>
          </div>
          
          {chatMessages.length > 0 && (
            <Button 
              variant="outline" 
              size="sm" 
              onClick={clearChat}
              className="w-full"
            >
              Clear Chat
            </Button>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
