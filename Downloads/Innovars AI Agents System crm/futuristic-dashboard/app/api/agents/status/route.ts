import { NextRequest, NextResponse } from 'next/server';

const AGENT_ZERO_BASE_URL = process.env.AGENT_ZERO_BASE_URL || 'http://localhost:50001';

export async function GET() {
  try {
    // Test connection to Agent Zero using health endpoint
    const response = await fetch(`${AGENT_ZERO_BASE_URL}/health`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      return NextResponse.json(
        {
          status: 'error',
          message: 'Agent Zero server not reachable',
          agentZeroUrl: AGENT_ZERO_BASE_URL,
          httpStatus: response.status
        },
        { status: 503 }
      );
    }

    const data = await response.json();

    return NextResponse.json({
      status: 'connected',
      agentZero: {
        health: data,
        url: AGENT_ZERO_BASE_URL,
        version: data.gitinfo?.version || 'unknown'
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error connecting to Agent Zero:', error);
    return NextResponse.json(
      {
        status: 'error',
        message: 'Failed to connect to Agent Zero',
        error: error instanceof Error ? error.message : 'Unknown error',
        agentZeroUrl: AGENT_ZERO_BASE_URL
      },
      { status: 500 }
    );
  }
}
