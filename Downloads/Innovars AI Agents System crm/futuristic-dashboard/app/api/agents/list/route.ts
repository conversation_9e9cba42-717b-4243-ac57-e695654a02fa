import { NextRequest, NextResponse } from 'next/server';

const AGENT_ZERO_BASE_URL = process.env.AGENT_ZERO_BASE_URL || 'http://localhost:50001';

// Mock agent data for CRM-specific agents
const CRM_AGENTS = [
  {
    id: 'lead-processor',
    name: 'Lead Processing Agent',
    description: 'Processes and qualifies incoming leads',
    status: 'active',
    type: 'crm-lead',
    capabilities: ['lead-qualification', 'data-enrichment', 'scoring'],
    performance: {
      tasksCompleted: 156,
      successRate: 94.2,
      avgResponseTime: '2.3s'
    }
  },
  {
    id: 'sales-assistant',
    name: 'Sales Assistant Agent',
    description: 'Assists with sales processes and customer interactions',
    status: 'active',
    type: 'crm-sales',
    capabilities: ['opportunity-management', 'follow-up', 'proposal-generation'],
    performance: {
      tasksCompleted: 89,
      successRate: 91.7,
      avgResponseTime: '1.8s'
    }
  },
  {
    id: 'customer-support',
    name: 'Customer Support Agent',
    description: 'Handles customer inquiries and support tickets',
    status: 'active',
    type: 'crm-support',
    capabilities: ['ticket-routing', 'knowledge-base', 'escalation'],
    performance: {
      tasksCompleted: 234,
      successRate: 96.1,
      avgResponseTime: '1.2s'
    }
  },
  {
    id: 'data-analyst',
    name: 'Data Analysis Agent',
    description: 'Analyzes CRM data and generates insights',
    status: 'active',
    type: 'crm-analytics',
    capabilities: ['data-analysis', 'reporting', 'trend-detection'],
    performance: {
      tasksCompleted: 67,
      successRate: 98.5,
      avgResponseTime: '4.1s'
    }
  }
];

export async function GET() {
  try {
    // Try to get agents from Agent Zero first
    let agentZeroAgents = [];
    try {
      const response = await fetch(`${AGENT_ZERO_BASE_URL}/api/agents`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        agentZeroAgents = data.agents || [];
      }
    } catch (error) {
      console.warn('Could not fetch agents from Agent Zero:', error);
    }

    // Combine Agent Zero agents with CRM-specific agents
    const allAgents = [
      ...CRM_AGENTS,
      ...agentZeroAgents.map((agent: any) => ({
        ...agent,
        type: 'agent-zero',
        source: 'agent-zero'
      }))
    ];

    return NextResponse.json({
      success: true,
      agents: allAgents,
      total: allAgents.length,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('Error fetching agents:', error);
    return NextResponse.json(
      { 
        error: 'Failed to fetch agents',
        message: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
