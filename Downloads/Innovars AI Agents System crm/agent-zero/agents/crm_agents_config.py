"""
CRM Agents Configuration
Configuration and initialization for specialized CRM agents
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from typing import Dict, Any, List
from crm_lead_processor import CRMLeadProcessor
from crm_sales_assistant import CRMSalesAssistant
from crm_customer_support import CRMCustomerSupport
from crm_data_analyst import CRMDataAnalyst

class CRMAgentsManager:
    """
    Manager class for CRM-specific agents
    Handles initialization, configuration, and coordination of CRM agents
    """
    
    def __init__(self):
        self.agents = {}
        self.agent_configs = {
            "lead_processor": {
                "class": CRMLeadProcessor,
                "description": "Processes and qualifies incoming leads",
                "capabilities": ["lead-qualification", "data-enrichment", "scoring"],
                "priority": "high",
                "auto_start": True
            },
            "sales_assistant": {
                "class": CRMSalesAssistant,
                "description": "Assists with sales processes and customer interactions",
                "capabilities": ["opportunity-management", "follow-up", "proposal-generation"],
                "priority": "high",
                "auto_start": True
            },
            "customer_support": {
                "class": CRMCustomerSupport,
                "description": "Handles customer inquiries and support tickets",
                "capabilities": ["ticket-routing", "knowledge-base", "escalation"],
                "priority": "medium",
                "auto_start": True
            },
            "data_analyst": {
                "class": CRMDataAnalyst,
                "description": "Analyzes CRM data and generates insights",
                "capabilities": ["data-analysis", "reporting", "trend-detection"],
                "priority": "medium",
                "auto_start": False  # On-demand analysis
            }
        }
    
    def initialize_agents(self) -> Dict[str, Any]:
        """Initialize all CRM agents"""
        initialized_agents = {}
        
        for agent_id, config in self.agent_configs.items():
            try:
                if config["auto_start"]:
                    agent_class = config["class"]
                    agent_instance = agent_class(
                        number=len(self.agents) + 1,
                        config={
                            "agent_id": agent_id,
                            "description": config["description"],
                            "capabilities": config["capabilities"],
                            "priority": config["priority"]
                        }
                    )
                    
                    self.agents[agent_id] = agent_instance
                    initialized_agents[agent_id] = {
                        "status": "initialized",
                        "agent": agent_instance,
                        "config": config
                    }
                else:
                    initialized_agents[agent_id] = {
                        "status": "available",
                        "agent": None,
                        "config": config
                    }
                    
            except Exception as e:
                initialized_agents[agent_id] = {
                    "status": "error",
                    "error": str(e),
                    "config": config
                }
        
        return initialized_agents
    
    def get_agent(self, agent_id: str) -> Any:
        """Get a specific agent instance"""
        if agent_id in self.agents:
            return self.agents[agent_id]
        
        # Initialize on-demand if not already initialized
        if agent_id in self.agent_configs:
            config = self.agent_configs[agent_id]
            agent_class = config["class"]
            agent_instance = agent_class(
                number=len(self.agents) + 1,
                config={
                    "agent_id": agent_id,
                    "description": config["description"],
                    "capabilities": config["capabilities"],
                    "priority": config["priority"]
                }
            )
            self.agents[agent_id] = agent_instance
            return agent_instance
        
        return None
    
    def get_available_agents(self) -> List[Dict[str, Any]]:
        """Get list of all available CRM agents"""
        agents_list = []
        
        for agent_id, config in self.agent_configs.items():
            agent_info = {
                "id": agent_id,
                "name": config["class"].__name__,
                "description": config["description"],
                "capabilities": config["capabilities"],
                "priority": config["priority"],
                "status": "active" if agent_id in self.agents else "available",
                "auto_start": config["auto_start"]
            }
            agents_list.append(agent_info)
        
        return agents_list
    
    def process_crm_request(self, request_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Route CRM requests to appropriate agents
        """
        routing_map = {
            "lead_processing": "lead_processor",
            "lead_qualification": "lead_processor",
            "opportunity_management": "sales_assistant",
            "sales_assistance": "sales_assistant",
            "customer_support": "customer_support",
            "ticket_processing": "customer_support",
            "data_analysis": "data_analyst",
            "reporting": "data_analyst",
            "analytics": "data_analyst"
        }
        
        agent_id = routing_map.get(request_type)
        if not agent_id:
            return {
                "error": f"No agent available for request type: {request_type}",
                "available_types": list(routing_map.keys())
            }
        
        agent = self.get_agent(agent_id)
        if not agent:
            return {
                "error": f"Failed to initialize agent: {agent_id}"
            }
        
        # Route to appropriate method based on request type
        try:
            import asyncio

            if request_type in ["lead_processing", "lead_qualification"]:
                return asyncio.run(agent.process_lead(data))
            elif request_type in ["opportunity_management", "sales_assistance"]:
                return asyncio.run(agent.manage_opportunity(data))
            elif request_type in ["customer_support", "ticket_processing"]:
                return asyncio.run(agent.process_ticket(data))
            elif request_type in ["data_analysis", "reporting", "analytics"]:
                return asyncio.run(agent.analyze_data(data))
            else:
                return {"error": f"Unknown request type: {request_type}"}

        except Exception as e:
            return {
                "error": f"Agent processing failed: {str(e)}",
                "agent_id": agent_id,
                "request_type": request_type
            }

# Global CRM agents manager instance
crm_manager = CRMAgentsManager()

def get_crm_agents_status() -> Dict[str, Any]:
    """Get status of all CRM agents"""
    return {
        "manager_status": "active",
        "available_agents": crm_manager.get_available_agents(),
        "initialized_agents": list(crm_manager.agents.keys()),
        "total_agents": len(crm_manager.agent_configs)
    }

def process_crm_request(request_type: str, data: Dict[str, Any]) -> Dict[str, Any]:
    """Process a CRM request through the appropriate agent"""
    return crm_manager.process_crm_request(request_type, data)

def initialize_crm_agents() -> Dict[str, Any]:
    """Initialize all CRM agents"""
    return crm_manager.initialize_agents()

# Example usage and testing functions
def test_lead_processing():
    """Test lead processing functionality"""
    sample_lead = {
        "id": "lead_001",
        "name": "John Doe",
        "email": "<EMAIL>",
        "company": "Example Corp",
        "phone": "******-0123",
        "title": "CTO",
        "industry": "Technology",
        "company_size": "medium",
        "budget_range": "high",
        "urgency": "medium",
        "fit_score": "good",
        "source": "website_form"
    }
    
    return process_crm_request("lead_processing", sample_lead)

def test_opportunity_management():
    """Test opportunity management functionality"""
    sample_opportunity = {
        "id": "opp_001",
        "name": "Example Corp - CRM Implementation",
        "value": 50000,
        "stage": "proposal",
        "probability": 60,
        "customer_tier": "premium",
        "days_in_current_stage": 10,
        "budget_confirmed": True,
        "decision_maker_identified": True,
        "timeline_defined": True,
        "competitors_involved": False
    }
    
    return process_crm_request("opportunity_management", sample_opportunity)

def test_support_ticket():
    """Test support ticket processing"""
    sample_ticket = {
        "id": "ticket_001",
        "subject": "Login issues with CRM system",
        "description": "User cannot access their account after password reset",
        "customer_name": "Jane Smith",
        "customer_tier": "enterprise",
        "priority": "high",
        "created_at": "2025-06-29T10:00:00Z"
    }
    
    return process_crm_request("ticket_processing", sample_ticket)

def test_data_analysis():
    """Test data analysis functionality"""
    sample_data = {
        "type": "sales_performance",
        "time_period": "last_30_days",
        "data": {
            "deals": [
                {"id": "deal_1", "value": 10000, "status": "won", "created_at": "2025-06-01T00:00:00Z", "closed_at": "2025-06-15T00:00:00Z"},
                {"id": "deal_2", "value": 15000, "status": "won", "created_at": "2025-06-05T00:00:00Z", "closed_at": "2025-06-20T00:00:00Z"},
                {"id": "deal_3", "value": 8000, "status": "lost", "created_at": "2025-06-10T00:00:00Z", "closed_at": "2025-06-25T00:00:00Z"}
            ],
            "activities": [
                {"id": "act_1", "type": "call", "date": "2025-06-15T00:00:00Z"},
                {"id": "act_2", "type": "email", "date": "2025-06-16T00:00:00Z"}
            ]
        }
    }
    
    return process_crm_request("data_analysis", sample_data)

if __name__ == "__main__":
    # Initialize agents
    print("Initializing CRM agents...")
    init_result = initialize_crm_agents()
    print(f"Initialization result: {init_result}")
    
    # Get status
    print("\nCRM Agents Status:")
    status = get_crm_agents_status()
    print(f"Status: {status}")
    
    # Test each agent
    print("\nTesting Lead Processing:")
    lead_result = test_lead_processing()
    print(f"Lead processing result: {lead_result}")
    
    print("\nTesting Opportunity Management:")
    opp_result = test_opportunity_management()
    print(f"Opportunity management result: {opp_result}")
    
    print("\nTesting Support Ticket:")
    ticket_result = test_support_ticket()
    print(f"Ticket processing result: {ticket_result}")
    
    print("\nTesting Data Analysis:")
    analysis_result = test_data_analysis()
    print(f"Data analysis result: {analysis_result}")
