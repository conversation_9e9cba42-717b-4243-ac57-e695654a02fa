"""
CRM Lead Processing Agent
Specialized agent for processing and qualifying incoming leads
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent import Agent, AgentContext
from python.helpers.print_style import PrintStyle
from python.helpers import files
import json
import re
from datetime import datetime
from typing import Dict, List, Any

class CRMLeadProcessor(Agent):
    """
    Specialized agent for CRM lead processing tasks including:
    - Lead qualification and scoring
    - Data enrichment and validation
    - Lead routing and assignment
    - Follow-up scheduling
    """
    
    def __init__(self, number: int, config: Dict[str, Any]):
        super().__init__(number, config)
        self.agent_name = "CRM Lead Processor"
        self.agent_description = "Processes and qualifies incoming leads for the CRM system"
        
        # Lead scoring criteria
        self.scoring_criteria = {
            "company_size": {"small": 1, "medium": 3, "large": 5, "enterprise": 7},
            "budget_range": {"low": 1, "medium": 3, "high": 5, "premium": 7},
            "urgency": {"low": 1, "medium": 3, "high": 5, "urgent": 7},
            "fit_score": {"poor": 1, "fair": 3, "good": 5, "excellent": 7}
        }
        
        # Lead statuses
        self.lead_statuses = [
            "new", "contacted", "qualified", "unqualified", 
            "nurturing", "converted", "lost"
        ]
    
    def get_system_prompt(self) -> str:
        return f"""You are {self.agent_name}, a specialized AI agent for CRM lead processing.

Your primary responsibilities:
1. **Lead Qualification**: Analyze incoming leads and determine their quality and potential
2. **Data Enrichment**: Enhance lead data with additional information and validation
3. **Lead Scoring**: Assign scores based on predefined criteria
4. **Routing & Assignment**: Determine the best sales representative for each lead
5. **Follow-up Planning**: Schedule appropriate follow-up actions

**Lead Scoring Criteria:**
{json.dumps(self.scoring_criteria, indent=2)}

**Available Lead Statuses:**
{', '.join(self.lead_statuses)}

**Key Guidelines:**
- Always validate email addresses and phone numbers
- Enrich company data when possible (industry, size, revenue)
- Score leads objectively based on the criteria
- Provide clear reasoning for qualification decisions
- Suggest specific follow-up actions and timelines
- Flag any data quality issues or missing information

**Response Format:**
When processing a lead, provide:
1. Lead qualification summary
2. Calculated lead score (1-28 scale)
3. Recommended status
4. Suggested actions
5. Data quality assessment

Be thorough, analytical, and always focus on maximizing conversion potential while maintaining data quality standards."""

    async def process_lead(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single lead through qualification pipeline
        """
        try:
            PrintStyle(self.agent_name).print(f"Processing lead: {lead_data.get('name', 'Unknown')}")
            
            # Validate required fields
            validation_result = self._validate_lead_data(lead_data)
            
            # Calculate lead score
            score = self._calculate_lead_score(lead_data)
            
            # Determine qualification status
            qualification = self._determine_qualification(score, lead_data)
            
            # Generate follow-up recommendations
            follow_up = self._generate_follow_up_plan(qualification, lead_data)
            
            # Enrich data if possible
            enriched_data = await self._enrich_lead_data(lead_data)
            
            result = {
                "lead_id": lead_data.get("id", f"lead_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                "original_data": lead_data,
                "enriched_data": enriched_data,
                "validation": validation_result,
                "score": score,
                "qualification": qualification,
                "follow_up_plan": follow_up,
                "processed_at": datetime.now().isoformat(),
                "processor": self.agent_name
            }
            
            PrintStyle(self.agent_name).print(f"Lead processed successfully. Score: {score}, Status: {qualification['status']}")
            return result
            
        except Exception as e:
            PrintStyle(self.agent_name).print(f"Error processing lead: {str(e)}")
            return {
                "error": str(e),
                "lead_data": lead_data,
                "processed_at": datetime.now().isoformat()
            }
    
    def _validate_lead_data(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Validate lead data quality and completeness"""
        validation = {
            "is_valid": True,
            "issues": [],
            "completeness_score": 0,
            "required_fields": ["name", "email", "company"]
        }
        
        # Check required fields
        for field in validation["required_fields"]:
            if not lead_data.get(field):
                validation["issues"].append(f"Missing required field: {field}")
                validation["is_valid"] = False
        
        # Validate email format
        email = lead_data.get("email", "")
        if email and not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
            validation["issues"].append("Invalid email format")
            validation["is_valid"] = False
        
        # Validate phone number if provided
        phone = lead_data.get("phone", "")
        if phone and not re.match(r'^[\+]?[1-9][\d]{0,15}$', re.sub(r'[\s\-\(\)]', '', phone)):
            validation["issues"].append("Invalid phone number format")
        
        # Calculate completeness score
        total_fields = len(["name", "email", "company", "phone", "title", "industry", "budget", "timeline"])
        completed_fields = sum(1 for field in total_fields if lead_data.get(field))
        validation["completeness_score"] = (completed_fields / len(total_fields)) * 100
        
        return validation
    
    def _calculate_lead_score(self, lead_data: Dict[str, Any]) -> int:
        """Calculate lead score based on predefined criteria"""
        score = 0
        
        # Company size scoring
        company_size = lead_data.get("company_size", "").lower()
        score += self.scoring_criteria["company_size"].get(company_size, 1)
        
        # Budget range scoring
        budget_range = lead_data.get("budget_range", "").lower()
        score += self.scoring_criteria["budget_range"].get(budget_range, 1)
        
        # Urgency scoring
        urgency = lead_data.get("urgency", "").lower()
        score += self.scoring_criteria["urgency"].get(urgency, 1)
        
        # Fit score (based on industry match, use case alignment, etc.)
        fit_score = lead_data.get("fit_score", "").lower()
        score += self.scoring_criteria["fit_score"].get(fit_score, 1)
        
        # Bonus points for completeness
        if lead_data.get("phone"):
            score += 1
        if lead_data.get("title"):
            score += 1
        if lead_data.get("industry"):
            score += 1
        if lead_data.get("timeline"):
            score += 1
        
        return min(score, 28)  # Cap at maximum possible score
    
    def _determine_qualification(self, score: int, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Determine lead qualification based on score and other factors"""
        if score >= 20:
            status = "qualified"
            priority = "high"
            reason = "High score indicates strong potential"
        elif score >= 15:
            status = "qualified"
            priority = "medium"
            reason = "Good score with moderate potential"
        elif score >= 10:
            status = "nurturing"
            priority = "low"
            reason = "Moderate score, needs nurturing"
        else:
            status = "unqualified"
            priority = "low"
            reason = "Low score indicates poor fit"
        
        return {
            "status": status,
            "priority": priority,
            "reason": reason,
            "confidence": min((score / 28) * 100, 100)
        }
    
    def _generate_follow_up_plan(self, qualification: Dict[str, Any], lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate follow-up action plan based on qualification"""
        if qualification["status"] == "qualified":
            if qualification["priority"] == "high":
                return {
                    "action": "immediate_contact",
                    "timeline": "within 1 hour",
                    "method": "phone_call",
                    "message": "High-priority qualified lead - immediate attention required"
                }
            else:
                return {
                    "action": "contact_within_day",
                    "timeline": "within 24 hours",
                    "method": "email_or_phone",
                    "message": "Qualified lead - schedule demo or consultation"
                }
        elif qualification["status"] == "nurturing":
            return {
                "action": "add_to_nurture_campaign",
                "timeline": "within 1 week",
                "method": "email_sequence",
                "message": "Add to nurturing campaign for gradual engagement"
            }
        else:
            return {
                "action": "archive_or_disqualify",
                "timeline": "immediate",
                "method": "system_update",
                "message": "Mark as unqualified and archive"
            }
    
    async def _enrich_lead_data(self, lead_data: Dict[str, Any]) -> Dict[str, Any]:
        """Enrich lead data with additional information"""
        enriched = lead_data.copy()
        
        # Add processing metadata
        enriched["enrichment"] = {
            "processed_by": self.agent_name,
            "enrichment_date": datetime.now().isoformat(),
            "data_sources": ["internal_validation", "format_standardization"]
        }
        
        # Standardize company name
        if enriched.get("company"):
            enriched["company_standardized"] = enriched["company"].strip().title()
        
        # Standardize industry if provided
        if enriched.get("industry"):
            enriched["industry_standardized"] = enriched["industry"].strip().title()
        
        # Add derived fields
        if enriched.get("email"):
            domain = enriched["email"].split("@")[-1] if "@" in enriched["email"] else ""
            enriched["email_domain"] = domain
            enriched["is_business_email"] = not any(consumer in domain.lower() 
                                                  for consumer in ["gmail", "yahoo", "hotmail", "outlook"])
        
        return enriched
