'use client';

import { useState, useEffect, useCallback } from 'react';

export interface Agent {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive' | 'busy' | 'error';
  type: string;
  capabilities?: string[];
  performance?: {
    tasksCompleted: number;
    successRate: number;
    avgResponseTime: string;
  };
}

export interface ChatMessage {
  id: string;
  message: string;
  sender: 'user' | 'agent';
  agentId?: string;
  timestamp: string;
}

export interface AgentZeroStatus {
  status: 'connected' | 'disconnected' | 'error';
  message?: string;
  timestamp?: string;
}

export function useAgentZero() {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [status, setStatus] = useState<AgentZeroStatus>({ status: 'disconnected' });
  const [loading, setLoading] = useState(false);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);

  // Check Agent Zero connection status
  const checkStatus = useCallback(async () => {
    try {
      const response = await fetch('/api/agents/status');
      const data = await response.json();
      
      if (response.ok && data.status === 'connected') {
        setStatus({ 
          status: 'connected', 
          timestamp: data.timestamp 
        });
      } else {
        setStatus({ 
          status: 'error', 
          message: data.message || 'Connection failed' 
        });
      }
    } catch (error) {
      setStatus({ 
        status: 'error', 
        message: error instanceof Error ? error.message : 'Unknown error' 
      });
    }
  }, []);

  // Fetch available agents
  const fetchAgents = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/agents/list');
      const data = await response.json();
      
      if (response.ok && data.success) {
        setAgents(data.agents);
      } else {
        console.error('Failed to fetch agents:', data.error);
      }
    } catch (error) {
      console.error('Error fetching agents:', error);
    } finally {
      setLoading(false);
    }
  }, []);

  // Send message to agent
  const sendMessage = useCallback(async (
    message: string, 
    agentId?: string, 
    sessionId?: string
  ) => {
    try {
      const userMessage: ChatMessage = {
        id: `user-${Date.now()}`,
        message,
        sender: 'user',
        agentId,
        timestamp: new Date().toISOString()
      };
      
      setChatMessages(prev => [...prev, userMessage]);

      const response = await fetch('/api/agents/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          message,
          agentId,
          sessionId
        }),
      });

      const data = await response.json();
      
      if (response.ok && data.success) {
        const agentMessage: ChatMessage = {
          id: `agent-${Date.now()}`,
          message: data.response.message || 'Response received',
          sender: 'agent',
          agentId,
          timestamp: data.timestamp
        };
        
        setChatMessages(prev => [...prev, agentMessage]);
        return data.response;
      } else {
        throw new Error(data.error || 'Failed to send message');
      }
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }, []);

  // Clear chat messages
  const clearChat = useCallback(() => {
    setChatMessages([]);
  }, []);

  // Initialize on mount
  useEffect(() => {
    checkStatus();
    fetchAgents();
  }, [checkStatus, fetchAgents]);

  return {
    agents,
    status,
    loading,
    chatMessages,
    checkStatus,
    fetchAgents,
    sendMessage,
    clearChat,
  };
}
