"""
CRM Data Analysis Agent
Specialized agent for CRM data analysis and business intelligence
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agent import Agent, AgentContext
from python.helpers.print_style import PrintStyle
from python.helpers import files
import json
import statistics
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

class CRMDataAnalyst(Agent):
    """
    Specialized agent for CRM data analysis including:
    - Sales performance analysis
    - Customer behavior analytics
    - Pipeline forecasting
    - Trend detection and reporting
    - KPI monitoring and alerts
    """
    
    def __init__(self, number: int, config: Dict[str, Any]):
        super().__init__(number, config)
        self.agent_name = "CRM Data Analyst"
        self.agent_description = "Analyzes CRM data and generates insights"
        
        # Key performance indicators
        self.kpis = {
            "sales_metrics": [
                "total_revenue", "average_deal_size", "win_rate", 
                "sales_cycle_length", "pipeline_value", "conversion_rate"
            ],
            "customer_metrics": [
                "customer_acquisition_cost", "customer_lifetime_value", 
                "churn_rate", "retention_rate", "satisfaction_score"
            ],
            "activity_metrics": [
                "calls_made", "emails_sent", "meetings_scheduled", 
                "demos_conducted", "proposals_sent"
            ],
            "pipeline_metrics": [
                "pipeline_velocity", "stage_conversion_rates", 
                "deal_progression", "forecast_accuracy"
            ]
        }
        
        # Analysis types
        self.analysis_types = {
            "trend_analysis": "Analyze trends over time periods",
            "comparative_analysis": "Compare performance across segments",
            "predictive_analysis": "Forecast future performance",
            "cohort_analysis": "Analyze customer cohorts",
            "funnel_analysis": "Analyze conversion funnels",
            "performance_analysis": "Analyze individual/team performance"
        }
        
        # Report templates
        self.report_templates = {
            "executive_summary": {
                "sections": ["key_metrics", "trends", "insights", "recommendations"],
                "format": "high_level"
            },
            "sales_performance": {
                "sections": ["revenue_analysis", "pipeline_analysis", "activity_analysis", "forecasting"],
                "format": "detailed"
            },
            "customer_analytics": {
                "sections": ["acquisition_analysis", "retention_analysis", "behavior_analysis", "segmentation"],
                "format": "detailed"
            }
        }
    
    def get_system_prompt(self) -> str:
        return f"""You are {self.agent_name}, a specialized AI agent for CRM data analysis and business intelligence.

Your primary responsibilities:
1. **Sales Analytics**: Analyze sales performance, trends, and forecasting
2. **Customer Analytics**: Analyze customer behavior, retention, and segmentation
3. **Pipeline Analysis**: Monitor and analyze sales pipeline health and velocity
4. **Performance Monitoring**: Track KPIs and generate alerts for anomalies
5. **Reporting**: Create comprehensive reports and dashboards
6. **Insights Generation**: Provide actionable insights and recommendations

**Key Performance Indicators:**
{json.dumps(self.kpis, indent=2)}

**Analysis Types:**
{json.dumps(self.analysis_types, indent=2)}

**Key Guidelines:**
- Always validate data quality before analysis
- Provide statistical significance for findings
- Include confidence intervals for predictions
- Highlight actionable insights
- Compare against historical benchmarks
- Identify trends, patterns, and anomalies
- Suggest specific improvement actions

**Response Format:**
When conducting analysis, provide:
1. Data quality assessment
2. Key findings and metrics
3. Trend analysis and patterns
4. Statistical insights
5. Actionable recommendations
6. Confidence levels for predictions

Be data-driven, objective, and focus on actionable insights that drive business value."""

    async def analyze_data(self, analysis_request: Dict[str, Any]) -> Dict[str, Any]:
        """
        Perform comprehensive data analysis based on request
        """
        try:
            analysis_type = analysis_request.get("type", "performance_analysis")
            data = analysis_request.get("data", {})
            time_period = analysis_request.get("time_period", "last_30_days")
            
            PrintStyle(self.agent_name).print(f"Performing {analysis_type} for {time_period}")
            
            # Validate data quality
            data_quality = self._assess_data_quality(data)
            
            # Perform requested analysis
            analysis_result = await self._perform_analysis(analysis_type, data, time_period)
            
            # Generate insights
            insights = self._generate_insights(analysis_result, analysis_type)
            
            # Create recommendations
            recommendations = self._create_recommendations(analysis_result, insights)
            
            # Calculate confidence metrics
            confidence_metrics = self._calculate_confidence_metrics(analysis_result, data_quality)
            
            result = {
                "analysis_id": f"analysis_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "request": analysis_request,
                "data_quality": data_quality,
                "analysis_result": analysis_result,
                "insights": insights,
                "recommendations": recommendations,
                "confidence_metrics": confidence_metrics,
                "analyzed_at": datetime.now().isoformat(),
                "analyst": self.agent_name
            }
            
            PrintStyle(self.agent_name).print(f"Analysis completed. Quality score: {data_quality['overall_score']}")
            return result
            
        except Exception as e:
            PrintStyle(self.agent_name).print(f"Error in data analysis: {str(e)}")
            return {
                "error": str(e),
                "analysis_request": analysis_request,
                "analyzed_at": datetime.now().isoformat()
            }
    
    def _assess_data_quality(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess the quality of input data"""
        quality_issues = []
        completeness_scores = {}
        
        # Check data completeness
        for category, datasets in data.items():
            if isinstance(datasets, list):
                total_records = len(datasets)
                if total_records == 0:
                    quality_issues.append(f"No data available for {category}")
                    completeness_scores[category] = 0
                else:
                    # Check for missing values in records
                    if datasets and isinstance(datasets[0], dict):
                        fields = datasets[0].keys()
                        missing_counts = {field: sum(1 for record in datasets if not record.get(field)) for field in fields}
                        completeness = {field: (total_records - missing) / total_records * 100 for field, missing in missing_counts.items()}
                        completeness_scores[category] = statistics.mean(completeness.values())
                        
                        # Flag fields with high missing rates
                        for field, rate in completeness.items():
                            if rate < 70:
                                quality_issues.append(f"High missing rate in {category}.{field}: {100-rate:.1f}%")
        
        # Calculate overall quality score
        overall_score = statistics.mean(completeness_scores.values()) if completeness_scores else 0
        
        return {
            "overall_score": round(overall_score, 2),
            "completeness_scores": completeness_scores,
            "quality_issues": quality_issues,
            "data_freshness": self._assess_data_freshness(data),
            "record_counts": {category: len(datasets) if isinstance(datasets, list) else 1 for category, datasets in data.items()}
        }
    
    def _assess_data_freshness(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Assess how fresh/recent the data is"""
        freshness_info = {}
        
        for category, datasets in data.items():
            if isinstance(datasets, list) and datasets:
                # Look for date fields
                date_fields = ["created_at", "updated_at", "date", "timestamp"]
                latest_date = None
                
                for record in datasets:
                    if isinstance(record, dict):
                        for field in date_fields:
                            if field in record and record[field]:
                                try:
                                    record_date = datetime.fromisoformat(record[field].replace('Z', '+00:00'))
                                    if latest_date is None or record_date > latest_date:
                                        latest_date = record_date
                                except:
                                    continue
                
                if latest_date:
                    age_hours = (datetime.now() - latest_date.replace(tzinfo=None)).total_seconds() / 3600
                    freshness_info[category] = {
                        "latest_record": latest_date.isoformat(),
                        "age_hours": round(age_hours, 2),
                        "freshness_rating": "fresh" if age_hours < 24 else "moderate" if age_hours < 168 else "stale"
                    }
        
        return freshness_info
    
    async def _perform_analysis(self, analysis_type: str, data: Dict[str, Any], time_period: str) -> Dict[str, Any]:
        """Perform the requested type of analysis"""
        if analysis_type == "sales_performance":
            return self._analyze_sales_performance(data, time_period)
        elif analysis_type == "customer_analytics":
            return self._analyze_customer_metrics(data, time_period)
        elif analysis_type == "pipeline_analysis":
            return self._analyze_pipeline(data, time_period)
        elif analysis_type == "trend_analysis":
            return self._analyze_trends(data, time_period)
        elif analysis_type == "forecasting":
            return self._perform_forecasting(data, time_period)
        else:
            return self._perform_general_analysis(data, time_period)
    
    def _analyze_sales_performance(self, data: Dict[str, Any], time_period: str) -> Dict[str, Any]:
        """Analyze sales performance metrics"""
        deals = data.get("deals", [])
        activities = data.get("activities", [])
        
        if not deals:
            return {"error": "No deals data available for sales analysis"}
        
        # Calculate key metrics
        total_revenue = sum(deal.get("value", 0) for deal in deals if deal.get("status") == "won")
        total_deals = len(deals)
        won_deals = len([deal for deal in deals if deal.get("status") == "won"])
        lost_deals = len([deal for deal in deals if deal.get("status") == "lost"])
        
        win_rate = (won_deals / max(1, won_deals + lost_deals)) * 100
        average_deal_size = total_revenue / max(1, won_deals)
        
        # Calculate sales cycle length
        cycle_lengths = []
        for deal in deals:
            if deal.get("status") == "won" and deal.get("created_at") and deal.get("closed_at"):
                try:
                    created = datetime.fromisoformat(deal["created_at"])
                    closed = datetime.fromisoformat(deal["closed_at"])
                    cycle_length = (closed - created).days
                    cycle_lengths.append(cycle_length)
                except:
                    continue
        
        avg_cycle_length = statistics.mean(cycle_lengths) if cycle_lengths else 0
        
        return {
            "total_revenue": total_revenue,
            "total_deals": total_deals,
            "won_deals": won_deals,
            "lost_deals": lost_deals,
            "win_rate": round(win_rate, 2),
            "average_deal_size": round(average_deal_size, 2),
            "average_cycle_length": round(avg_cycle_length, 1),
            "revenue_per_day": round(total_revenue / 30, 2),  # Assuming 30-day period
            "activity_count": len(activities)
        }
    
    def _analyze_customer_metrics(self, data: Dict[str, Any], time_period: str) -> Dict[str, Any]:
        """Analyze customer-related metrics"""
        customers = data.get("customers", [])
        interactions = data.get("interactions", [])
        
        if not customers:
            return {"error": "No customer data available for analysis"}
        
        # Customer acquisition analysis
        new_customers = len([c for c in customers if c.get("status") == "new"])
        total_customers = len(customers)
        
        # Customer value analysis
        customer_values = [c.get("lifetime_value", 0) for c in customers if c.get("lifetime_value")]
        avg_customer_value = statistics.mean(customer_values) if customer_values else 0
        
        # Interaction analysis
        interactions_per_customer = len(interactions) / max(1, total_customers)
        
        # Satisfaction analysis
        satisfaction_scores = [c.get("satisfaction_score", 0) for c in customers if c.get("satisfaction_score")]
        avg_satisfaction = statistics.mean(satisfaction_scores) if satisfaction_scores else 0
        
        return {
            "total_customers": total_customers,
            "new_customers": new_customers,
            "acquisition_rate": round((new_customers / max(1, total_customers)) * 100, 2),
            "average_customer_value": round(avg_customer_value, 2),
            "interactions_per_customer": round(interactions_per_customer, 2),
            "average_satisfaction": round(avg_satisfaction, 2),
            "high_value_customers": len([c for c in customers if c.get("lifetime_value", 0) > avg_customer_value])
        }
    
    def _analyze_pipeline(self, data: Dict[str, Any], time_period: str) -> Dict[str, Any]:
        """Analyze sales pipeline metrics"""
        opportunities = data.get("opportunities", [])
        
        if not opportunities:
            return {"error": "No pipeline data available for analysis"}
        
        # Pipeline value by stage
        pipeline_by_stage = {}
        stage_counts = {}
        
        for opp in opportunities:
            stage = opp.get("stage", "unknown")
            value = opp.get("value", 0)
            
            pipeline_by_stage[stage] = pipeline_by_stage.get(stage, 0) + value
            stage_counts[stage] = stage_counts.get(stage, 0) + 1
        
        # Calculate conversion rates between stages
        total_pipeline_value = sum(pipeline_by_stage.values())
        
        # Pipeline velocity (deals moving through stages)
        velocity_data = []
        for opp in opportunities:
            if opp.get("days_in_current_stage"):
                velocity_data.append(opp["days_in_current_stage"])
        
        avg_velocity = statistics.mean(velocity_data) if velocity_data else 0
        
        return {
            "total_pipeline_value": total_pipeline_value,
            "pipeline_by_stage": pipeline_by_stage,
            "stage_counts": stage_counts,
            "average_velocity": round(avg_velocity, 1),
            "total_opportunities": len(opportunities),
            "weighted_pipeline": sum(opp.get("value", 0) * (opp.get("probability", 50) / 100) for opp in opportunities)
        }
    
    def _analyze_trends(self, data: Dict[str, Any], time_period: str) -> Dict[str, Any]:
        """Analyze trends in the data"""
        # This would typically involve time-series analysis
        # For now, providing a simplified trend analysis
        
        trends = {
            "revenue_trend": "stable",  # Would calculate based on time-series data
            "customer_growth_trend": "increasing",
            "activity_trend": "stable",
            "pipeline_trend": "increasing"
        }
        
        return {
            "trends_identified": trends,
            "trend_confidence": "medium",
            "analysis_period": time_period,
            "trend_summary": "Overall positive trends in customer growth and pipeline"
        }
    
    def _perform_forecasting(self, data: Dict[str, Any], time_period: str) -> Dict[str, Any]:
        """Perform forecasting based on historical data"""
        # Simplified forecasting - would use more sophisticated models in practice
        deals = data.get("deals", [])
        opportunities = data.get("opportunities", [])
        
        # Revenue forecast based on current pipeline
        pipeline_value = sum(opp.get("value", 0) * (opp.get("probability", 50) / 100) for opp in opportunities)
        
        # Historical win rate
        won_deals = len([d for d in deals if d.get("status") == "won"])
        total_closed = len([d for d in deals if d.get("status") in ["won", "lost"]])
        historical_win_rate = (won_deals / max(1, total_closed)) * 100
        
        return {
            "forecasted_revenue": round(pipeline_value, 2),
            "forecast_confidence": "medium",
            "historical_win_rate": round(historical_win_rate, 2),
            "forecast_period": "next_30_days",
            "methodology": "pipeline_weighted_probability"
        }
    
    def _perform_general_analysis(self, data: Dict[str, Any], time_period: str) -> Dict[str, Any]:
        """Perform general analysis when specific type not specified"""
        summary = {}
        
        for category, dataset in data.items():
            if isinstance(dataset, list):
                summary[category] = {
                    "record_count": len(dataset),
                    "data_type": "list",
                    "sample_fields": list(dataset[0].keys()) if dataset and isinstance(dataset[0], dict) else []
                }
        
        return {
            "data_summary": summary,
            "analysis_type": "general_overview",
            "total_records": sum(len(d) for d in data.values() if isinstance(d, list))
        }
    
    def _generate_insights(self, analysis_result: Dict[str, Any], analysis_type: str) -> List[Dict[str, Any]]:
        """Generate actionable insights from analysis results"""
        insights = []
        
        if analysis_type == "sales_performance":
            win_rate = analysis_result.get("win_rate", 0)
            if win_rate < 20:
                insights.append({
                    "type": "concern",
                    "message": f"Win rate of {win_rate}% is below industry average",
                    "priority": "high",
                    "category": "sales_performance"
                })
            elif win_rate > 40:
                insights.append({
                    "type": "positive",
                    "message": f"Excellent win rate of {win_rate}%",
                    "priority": "medium",
                    "category": "sales_performance"
                })
            
            cycle_length = analysis_result.get("average_cycle_length", 0)
            if cycle_length > 90:
                insights.append({
                    "type": "opportunity",
                    "message": f"Sales cycle of {cycle_length} days could be optimized",
                    "priority": "medium",
                    "category": "process_improvement"
                })
        
        return insights
    
    def _create_recommendations(self, analysis_result: Dict[str, Any], insights: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create actionable recommendations based on analysis and insights"""
        recommendations = []
        
        # Generate recommendations based on insights
        for insight in insights:
            if insight["type"] == "concern" and insight["category"] == "sales_performance":
                recommendations.append({
                    "action": "Implement sales training program",
                    "priority": "high",
                    "timeline": "immediate",
                    "expected_impact": "Improve win rate by 10-15%"
                })
            elif insight["category"] == "process_improvement":
                recommendations.append({
                    "action": "Review and optimize sales process",
                    "priority": "medium",
                    "timeline": "within_30_days",
                    "expected_impact": "Reduce sales cycle by 20-30%"
                })
        
        return recommendations
    
    def _calculate_confidence_metrics(self, analysis_result: Dict[str, Any], data_quality: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate confidence metrics for the analysis"""
        base_confidence = data_quality["overall_score"] / 100
        
        # Adjust confidence based on data volume
        total_records = sum(data_quality["record_counts"].values())
        volume_factor = min(1.0, total_records / 100)  # Full confidence at 100+ records
        
        final_confidence = base_confidence * volume_factor
        
        return {
            "overall_confidence": round(final_confidence * 100, 2),
            "data_quality_factor": round(base_confidence * 100, 2),
            "volume_factor": round(volume_factor * 100, 2),
            "confidence_level": "high" if final_confidence > 0.8 else "medium" if final_confidence > 0.6 else "low"
        }
